// API Configuration
const API_BASE_URL = 'http://localhost:5000';

// Global variables
let currentUser = null;
let cart = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkSession();
    loadProducts();
    updateCartDisplay();
    setupEventListeners();
});

// Check if user is logged in
async function checkSession() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/check-session`, {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.authenticated) {
                currentUser = data.user;
                updateUserInterface();
            }
        }
    } catch (error) {
        console.log('Session check failed:', error);
    }
}

// Update UI based on user login status
function updateUserInterface() {
    const userLinks = document.querySelector('.user-links');
    if (currentUser) {
        userLinks.innerHTML = `
            <span>Welcome, ${currentUser.full_name}!</span>
            <a href="#" onclick="logout()" class="face-button">Logout</a>
            ${currentUser.is_admin ? '<a href="admin.html" class="face-button">Admin</a>' : ''}
        `;
    }
}

// Logout function
async function logout() {
    try {
        await fetch(`${API_BASE_URL}/api/auth/logout`, {
            method: 'POST',
            credentials: 'include'
        });
        
        currentUser = null;
        localStorage.removeItem('user');
        window.location.reload();
    } catch (error) {
        console.error('Logout error:', error);
    }
}

// Load products from API
async function loadProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/products?per_page=6`, {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            displayProducts(data.products);
        }
    } catch (error) {
        console.error('Failed to load products:', error);
        // Show fallback products
        displayFallbackProducts();
    }
}

// Display products in the grid
function displayProducts(products) {
    const productGrid = document.querySelector('.product-grid');
    if (!productGrid) return;
    
    productGrid.innerHTML = '';
    
    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.innerHTML = `
            <img src="${product.image_url || 'image/placeholder.jpg'}" alt="${product.name}" onerror="this.src='image/placeholder.jpg'">
            <h3>${product.name}</h3>
            <p class="price">$${product.price}</p>
            <p class="description">${product.description || ''}</p>
            <div class="product-actions">
                <button onclick="addToCart(${product.id}, '${product.name}', ${product.price})" 
                        ${!product.is_in_stock ? 'disabled' : ''}>
                    ${product.is_in_stock ? 'Add to Cart' : 'Out of Stock'}
                </button>
            </div>
        `;
        productGrid.appendChild(productCard);
    });
}

// Fallback products for when API is not available
function displayFallbackProducts() {
    const productGrid = document.querySelector('.product-grid');
    if (!productGrid) return;
    
    const fallbackProducts = [
        { id: 1, name: 'Product 1', price: 20.00, image: 'image/product1.jpg' },
        { id: 2, name: 'Product 2', price: 25.00, image: 'image/product2.jpg' },
        { id: 3, name: 'Product 3', price: 30.00, image: 'image/product3.jpg' }
    ];
    
    productGrid.innerHTML = '';
    
    fallbackProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.innerHTML = `
            <img src="${product.image}" alt="${product.name}" onerror="this.src='image/placeholder.jpg'">
            <h3>${product.name}</h3>
            <p class="price">$${product.price}</p>
            <button onclick="addToCart(${product.id}, '${product.name}', ${product.price})">Add to Cart</button>
        `;
        productGrid.appendChild(productCard);
    });
}

// Add item to cart
async function addToCart(productId, productName, price) {
    if (!currentUser) {
        alert('Please log in to add items to cart');
        window.location.href = 'login.html';
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/cart/add`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                product_id: productId,
                quantity: 1
            })
        });
        
        if (response.ok) {
            showNotification('Item added to cart!');
            updateCartDisplay();
        } else {
            const data = await response.json();
            alert(data.error || 'Failed to add item to cart');
        }
    } catch (error) {
        console.error('Add to cart error:', error);
        // Fallback to local storage
        addToLocalCart(productId, productName, price);
    }
}

// Fallback cart functionality using localStorage
function addToLocalCart(productId, productName, price) {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: price,
            quantity: 1
        });
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    showNotification('Item added to cart!');
    updateCartDisplay();
}

// Update cart display
function updateCartDisplay() {
    // This would update cart count in header
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
    }
}

// Show notification
function showNotification(message) {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px;
        border-radius: 5px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Setup event listeners
function setupEventListeners() {
    // Add any additional event listeners here
}

// CSS for notification animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
    
    .notification {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .product-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin: 10px;
        text-align: center;
        transition: transform 0.2s;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .product-card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 5px;
    }
    
    .product-card .price {
        font-size: 1.2em;
        font-weight: bold;
        color: #007BFF;
    }
    
    .product-card button {
        background: #007BFF;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: background 0.2s;
    }
    
    .product-card button:hover:not(:disabled) {
        background: #0056b3;
    }
    
    .product-card button:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
`;
document.head.appendChild(style);
