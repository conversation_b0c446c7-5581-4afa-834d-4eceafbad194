// API Configuration
const API_BASE_URL = 'http://localhost:5000';

document.getElementById('registerForm').addEventListener('submit', async function(event) {
    event.preventDefault();

    const fullName = document.getElementById('fullName').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const messageElement = document.getElementById('message');

    // Clear previous messages
    messageElement.innerText = '';

    // Basic validation
    if (!fullName || !email || !password) {
        messageElement.style.color = 'red';
        messageElement.innerText = 'All fields are required.';
        return;
    }

    if (password.length < 8) {
        messageElement.style.color = 'red';
        messageElement.innerText = 'Password must be at least 8 characters long.';
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                username: email.split('@')[0], // Use email prefix as username
                email: email,
                password: password,
                full_name: fullName
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Registration successful
            messageElement.style.color = 'green';
            messageElement.innerText = 'Registration successful! Redirecting to login...';

            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        } else {
            // Registration failed
            messageElement.style.color = 'red';
            messageElement.innerText = data.error || 'Registration failed. Please try again.';
        }
    } catch (error) {
        console.error('Registration error:', error);
        messageElement.style.color = 'red';
        messageElement.innerText = 'Network error. Please check your connection and try again.';
    }
});