body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: darkred; /* Background color for header */
    color: #333; /* Text color */
}

.logo {
    font-size: 2rem;
    font-weight: bold;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav ul li a {
    color: white;
    text-decoration: none;
}

.cart {
    max-width: 800px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.cart h2 {
    text-align: center;
}

.cart-items {
    margin: 20px 0;
}

.cart-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.cart-item img {
    width: 100px;
    height: auto;
    margin-right: 20px;
}

.item-details {
    flex-grow: 1;
}

.item-details h3 {
    margin: 0;
}

.item-details p {
    margin: 5px 0;
}

input[type="number"] {
    width: 60px;
    margin-right: 10px;
}

.cart-summary {
    text-align: center;
    margin-top: 20px;
}

footer {
    text-align: center;
    padding: 15px;
    background-color: #000; /* Footer background */
    color: white; /* Footer text color */
}

.face-button {
    display: inline-block;
    width: 50px; /* Button size */
    height: 50px; /* Button size */
    background-color: #007BFF; /* Background color */
    color: white; /* Text color */
    border-radius: 50%; /* Make it circular */
    text-align: center; /* Center text */
    line-height: 50px; /* Center text vertically */
    margin-left: 10px; /* Space between buttons */
    text-decoration: none; /* Remove underline */
    transition: background-color 0.3s; /* Smooth transition */
}

.face-button:hover {
    background-color: #0056b3; /* Darker blue on hover */
}