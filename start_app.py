#!/usr/bin/env python3
"""
Startup script for the Black Shadow E-commerce Platform
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_cors
        import werkzeug
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to install dependencies")
        return False

def setup_database():
    """Setup and seed the database"""
    print("Setting up database...")
    try:
        # Change to backend directory
        os.chdir('backend')
        
        # Run the seed script
        subprocess.check_call([sys.executable, "seed_data.py"])
        print("✓ Database setup completed")
        
        # Change back to root directory
        os.chdir('..')
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to setup database")
        os.chdir('..')
        return False

def start_server():
    """Start the Flask development server"""
    print("Starting Black Shadow E-commerce Platform...")
    print("Server will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    
    try:
        os.chdir('backend')
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n✓ Server stopped")
    except Exception as e:
        print(f"✗ Failed to start server: {e}")
    finally:
        os.chdir('..')

def main():
    print("=" * 50)
    print("BLACK SHADOW E-COMMERCE PLATFORM")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('backend') or not os.path.exists('Frontend'):
        print("✗ Please run this script from the project root directory")
        return
    
    # Check dependencies
    if not check_dependencies():
        print("\nAttempting to install dependencies...")
        if not install_dependencies():
            print("Please install dependencies manually using: pip install -r requirements.txt")
            return
    
    # Setup database
    if not setup_database():
        print("Database setup failed. Please check the error messages above.")
        return
    
    print("\n" + "=" * 50)
    print("SETUP COMPLETE!")
    print("=" * 50)
    print("Default login credentials:")
    print("Admin: <EMAIL> / admin123")
    print("User:  <EMAIL> / user123")
    print("=" * 50)
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
