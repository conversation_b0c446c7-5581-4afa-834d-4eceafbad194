/* styles.css */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-image: url(''); /* Specify your background image path */
    background-size: cover; /* Ensure the image covers the whole body */
    background-position: center; /* Center the image */
    background-repeat: no-repeat; /* Prevent the image from repeating */
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: darkred; /* Background color for header */
    color: #333; /* Text color */
}

.logo {
    font-size: 2rem;
    font-weight: bold;
}

nav ul {
    list-style-type: none;
    display: flex;
    gap: 15px; /* Space between navigation items */
}

nav a {
    color: white;
    text-decoration: none;
}

.user-links {
    position: relative;
}

.face-button {
    display: inline-block;
    width: 50px; /* Button size */
    height: 50px; /* Button size */
    background-color: #007BFF; /* Background color */
    color: white; /* Text color */
    border-radius: 50%; /* Make it circular */
    text-align: center; /* Center text */
    line-height: 50px; /* Center text vertically */
    margin-left: 10px; /* Space between buttons */
    text-decoration: none; /* Remove underline */
    transition: background-color 0.3s; /* Smooth transition */
}

.face-button:hover {
    background-color: #0056b3; /* Darker blue on hover */
}

.hero {
    text-align: center;
    padding: 50px 0; /* Add padding to the hero section */
    background-color: white; /* Light background for hero */
    background-image: url('');
}

.products {
    padding: 20px;
}

.product-grid {
    display: flex;
    gap: 20px; /* Space between product cards */
    flex-wrap: wrap; /* Allow wrapping of product cards */
}

.product-card {
    border: 1px solid #ccc; /* Border for cards */
    padding: 10px;
    border-radius: 8px; /* Rounded corners */
    text-align: center; /* Center text */
    width: 200px; /* Fixed width for cards */
}

.product-card img {
    width: 100%; /* Make image take full width */
    height: 150px; /* Fixed height to maintain uniformity */
    object-fit: cover; /* Cover the container, maintaining aspect ratio */
    border-radius: 5px; /* Rounded corners for images */
}

.product-card h3 {
    margin: 10px 0 5px; /* Margin for product title */
}

.product-card p {
    margin: 0 0 10px; /* Margin for product price */
}

.product-card button {
    padding: 10px;
    background-color: #007BFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.product-card button:hover {
    background-color: #0056b3;
}

footer {
    text-align: center;
    padding: 15px;
    background-color: #000; /* Footer background */
    color: white; /* Footer text color */
}
