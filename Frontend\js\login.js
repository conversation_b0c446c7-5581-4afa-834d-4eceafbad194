// API Configuration
const API_BASE_URL = 'http://localhost:5000';

// Check if user is already logged in
document.addEventListener('DOMContentLoaded', function() {
    checkSession();
});

async function checkSession() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/check-session`, {
            method: 'GET',
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.authenticated) {
                // User is already logged in, redirect to home
                window.location.href = 'index.html';
            }
        }
    } catch (error) {
        console.log('Session check failed:', error);
    }
}

document.getElementById('loginForm').addEventListener('submit', async function(event) {
    event.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const messageElement = document.getElementById('message');

    // Clear previous messages
    messageElement.innerText = '';

    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                username: email,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Login successful
            messageElement.style.color = 'green';
            messageElement.innerText = 'Login successful! Redirecting...';

            // Store user info in localStorage
            localStorage.setItem('user', JSON.stringify(data.user));

            // Redirect after a short delay
            setTimeout(() => {
                if (data.user.is_admin) {
                    window.location.href = 'admin.html';
                } else {
                    window.location.href = 'index.html';
                }
            }, 1000);
        } else {
            // Login failed
            messageElement.style.color = 'red';
            messageElement.innerText = data.error || 'Login failed. Please try again.';
        }
    } catch (error) {
        console.error('Login error:', error);
        messageElement.style.color = 'red';
        messageElement.innerText = 'Network error. Please check your connection and try again.';
    }
});