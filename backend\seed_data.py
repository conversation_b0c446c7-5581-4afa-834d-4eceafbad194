#!/usr/bin/env python3
"""
Seed script to populate the database with sample data for the e-commerce platform.
"""

from app import create_app
from models import db, User, Category, Product
from datetime import datetime

def seed_database():
    app = create_app('development')
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Create admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='Administrator',
            is_admin=True,
            email_verified=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        
        # Create regular user
        user = User(
            username='user',
            email='<EMAIL>',
            full_name='<PERSON>',
            phone='+1234567890',
            address='123 Main St',
            city='New York',
            postal_code='10001',
            country='USA',
            email_verified=True
        )
        user.set_password('user123')
        db.session.add(user)
        
        # Create categories
        categories = [
            Category(name='Electronics', description='Electronic devices and gadgets'),
            Category(name='Clothing', description='Fashion and apparel'),
            Category(name='Books', description='Books and educational materials'),
            Category(name='Home & Garden', description='Home improvement and gardening'),
            Category(name='Sports', description='Sports equipment and accessories'),
        ]
        
        for category in categories:
            db.session.add(category)
        
        db.session.commit()
        
        # Create products
        products = [
            # Electronics
            Product(
                name='Smartphone Pro',
                description='Latest smartphone with advanced features',
                price=899.99,
                cost_price=600.00,
                sku='PHONE-001',
                stock_quantity=50,
                category_id=1,
                is_featured=True,
                image_url='/static/images/smartphone.jpg'
            ),
            Product(
                name='Wireless Headphones',
                description='High-quality wireless headphones with noise cancellation',
                price=199.99,
                cost_price=120.00,
                sku='AUDIO-001',
                stock_quantity=30,
                category_id=1,
                image_url='/static/images/headphones.jpg'
            ),
            Product(
                name='Laptop Computer',
                description='Powerful laptop for work and gaming',
                price=1299.99,
                cost_price=900.00,
                sku='COMP-001',
                stock_quantity=25,
                category_id=1,
                is_featured=True,
                image_url='/static/images/laptop.jpg'
            ),
            
            # Clothing
            Product(
                name='Designer T-Shirt',
                description='Premium cotton t-shirt with unique design',
                price=29.99,
                cost_price=15.00,
                sku='CLOTH-001',
                stock_quantity=100,
                category_id=2,
                image_url='/static/images/tshirt.jpg'
            ),
            Product(
                name='Denim Jeans',
                description='Classic blue denim jeans',
                price=79.99,
                cost_price=40.00,
                sku='CLOTH-002',
                stock_quantity=75,
                category_id=2,
                image_url='/static/images/jeans.jpg'
            ),
            
            # Books
            Product(
                name='Programming Guide',
                description='Complete guide to modern programming',
                price=49.99,
                cost_price=25.00,
                sku='BOOK-001',
                stock_quantity=40,
                category_id=3,
                image_url='/static/images/book.jpg'
            ),
            
            # Home & Garden
            Product(
                name='Garden Tool Set',
                description='Complete set of gardening tools',
                price=89.99,
                cost_price=50.00,
                sku='GARDEN-001',
                stock_quantity=20,
                category_id=4,
                image_url='/static/images/tools.jpg'
            ),
            
            # Sports
            Product(
                name='Yoga Mat',
                description='Premium yoga mat for exercise',
                price=39.99,
                cost_price=20.00,
                sku='SPORT-001',
                stock_quantity=60,
                category_id=5,
                image_url='/static/images/yoga.jpg'
            ),
        ]
        
        for product in products:
            db.session.add(product)
        
        db.session.commit()
        
        print("Database seeded successfully!")
        print(f"Created {len(categories)} categories")
        print(f"Created {len(products)} products")
        print("Admin user: admin / admin123")
        print("Regular user: user / user123")

if __name__ == '__main__':
    seed_database()
