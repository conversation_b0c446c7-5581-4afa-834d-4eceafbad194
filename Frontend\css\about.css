* {
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #333;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: darkred; /* Background color for header */
    color: #333; /* Text color */
}

.logo {
    font-size: 2rem;
    font-weight: bold;
}

.logo {
    font-size: 2em;
    font-weight: bold;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav a {
    color: white;
    text-decoration: none;
}

main {
    padding: 20px;
    max-width: 1000px;
    margin: auto;
    background-color: white;
}

.about-section h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-align: center;
}

.about-section h2 {
    font-size: 1.8em;
    margin-top: 30px;
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
}

.about-section p {
    line-height: 1.6;
    margin: 10px 0;
}

.about-section ul {
    margin: 15px 0;
    padding-left: 20px;
}

.about-section li {
    margin-bottom: 10px;
}

.team-section {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin: 20px 0;
}

.team-member {
    text-align: center;
    margin: 10px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f0f0f0;
    width: 200px; /* Fixed width for team members */
}

.team-member img {
    width: 100%;
    height: auto;
    border-radius: 50%; /* Circular images */
}

.team-member h4 {
    margin: 10px 0 5px;
}

blockquote {
    border-left: 4px solid #007bff; /* Blue color for the border */
    padding-left: 15px;
    margin: 20px 0;
    font-style: italic;
}

footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1em 0;
    position: relative;
    bottom: 0;
    width: 100%;
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .team-section {
        flex-direction: column; /* Stack team members on smaller screens */
        align-items: center; /* Center-align the team members */
    }

    .team-member {
        width: 80%; /* Full width on smaller screens */
    }
}