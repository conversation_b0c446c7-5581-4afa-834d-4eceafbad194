body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: darkred; /* Background color for header */
    color: #333; /* Text color */
}

.logo {
    font-size: 2rem;
    font-weight: bold;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav ul li a {
    color: white;
    text-decoration: none;
}

.product-gallery {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.product-gallery h2 {
    text-align: center;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.product-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    transition: box-shadow 0.3s;
}

.product-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.product-card img {
    width: 100%;
    height: auto;
}

.product-card h3 {
    margin: 10px 0 5px;
}

.product-card p {
    margin: 5px 0 15px;
}

button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #218838;
}

footer {
    text-align: center;
    padding: 15px;
    background-color: #000; /* Footer background */
    color: white; /* Footer text color */
}

.face-button {
    display: inline-block;
    width: 50px; /* Button size */
    height: 50px; /* Button size */
    background-color: #007BFF; /* Background color */
    color: white; /* Text color */
    border-radius: 50%; /* Make it circular */
    text-align: center; /* Center text */
    line-height: 50px; /* Center text vertically */
    margin-left: 10px; /* Space between buttons */
    text-decoration: none; /* Remove underline */
    transition: background-color 0.3s; /* Smooth transition */
}

.face-button:hover {
    background-color: #0056b3; /* Darker blue on hover */
}