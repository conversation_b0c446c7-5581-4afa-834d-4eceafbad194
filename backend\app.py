from flask import Flask, render_template, send_from_directory, request
from flask_cors import CORS
import os
from database import init_db
from routes import register_routes
from auth import auth_bp
from config import config

# Optional rate limiting
try:
    from flask_limiter import Limiter
    from flask_limiter.util import get_remote_address
    LIMITER_AVAILABLE = True
except ImportError:
    LIMITER_AVAILABLE = False

def create_app(config_name='development'):
    app = Flask(__name__,
                static_folder='../Frontend',
                template_folder='../Frontend')

    # Load configuration
    app.config.from_object(config[config_name])

    # Enable CORS for API routes
    CORS(app, supports_credentials=True)

    # Rate limiting (optional)
    if LIMITER_AVAILABLE:
        try:
            limiter = Limiter(
                app,
                key_func=get_remote_address,
                default_limits=["200 per day", "50 per hour"]
            )

            # Apply stricter limits to auth endpoints
            @limiter.limit("5 per minute")
            def auth_rate_limit():
                pass
            print("Rate limiting enabled")
        except Exception as e:
            print(f"Rate limiting setup failed: {e}")
    else:
        print("Rate limiting not available - install Flask-Limiter for production")

    # Security headers
    @app.after_request
    def add_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
        return response

    # Create upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Initialize database
    init_db(app)

    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')

    # Register API routes
    register_routes(app)

    # Serve static files and frontend routes
    @app.route('/')
    def index():
        return send_from_directory(app.static_folder, 'index.html')

    @app.route('/<path:filename>')
    def serve_static(filename):
        return send_from_directory(app.static_folder, filename)

    # Admin routes for managing data
    @app.route("/api/admin/orders", methods=["GET"])
    def admin_get_orders():
        from auth import admin_required
        from flask import request, jsonify, session
        from models import Order

        @admin_required
        def _admin_get_orders():
            try:
                page = request.args.get('page', 1, type=int)
                per_page = request.args.get('per_page', app.config['ORDERS_PER_PAGE'], type=int)
                status = request.args.get('status')

                query = Order.query
                if status:
                    query = query.filter_by(status=status)

                orders = query.order_by(Order.created_at.desc()).paginate(
                    page=page, per_page=per_page, error_out=False
                )

                return jsonify({
                    'orders': [order.to_dict() for order in orders.items],
                    'pagination': {
                        'page': orders.page,
                        'pages': orders.pages,
                        'per_page': orders.per_page,
                        'total': orders.total,
                        'has_next': orders.has_next,
                        'has_prev': orders.has_prev
                    }
                }), 200

            except Exception as e:
                return jsonify({'error': 'Failed to fetch orders'}), 500

        return _admin_get_orders()

    @app.route("/api/admin/orders/<int:order_id>/status", methods=["PUT"])
    def admin_update_order_status(order_id):
        from auth import admin_required
        from flask import request, jsonify
        from models import Order, db
        from datetime import datetime

        @admin_required
        def _admin_update_order_status():
            try:
                order = Order.query.get(order_id)
                if not order:
                    return jsonify({'error': 'Order not found'}), 404

                data = request.get_json()
                new_status = data.get('status')

                if new_status not in ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']:
                    return jsonify({'error': 'Invalid status'}), 400

                order.status = new_status

                # Update timestamps based on status
                if new_status == 'shipped' and not order.shipped_at:
                    order.shipped_at = datetime.utcnow()
                elif new_status == 'delivered' and not order.delivered_at:
                    order.delivered_at = datetime.utcnow()

                order.updated_at = datetime.utcnow()
                db.session.commit()

                return jsonify({
                    'message': 'Order status updated successfully',
                    'order': order.to_dict()
                }), 200

            except Exception as e:
                db.session.rollback()
                return jsonify({'error': 'Failed to update order status'}), 500

        return _admin_update_order_status()

    @app.route("/api/admin/users", methods=["GET"])
    def admin_get_users():
        from auth import admin_required
        from flask import request, jsonify
        from models import User

        @admin_required
        def _admin_get_users():
            try:
                page = request.args.get('page', 1, type=int)
                per_page = request.args.get('per_page', app.config['USERS_PER_PAGE'], type=int)

                users = User.query.order_by(User.created_at.desc()).paginate(
                    page=page, per_page=per_page, error_out=False
                )

                return jsonify({
                    'users': [user.to_dict() for user in users.items],
                    'pagination': {
                        'page': users.page,
                        'pages': users.pages,
                        'per_page': users.per_page,
                        'total': users.total,
                        'has_next': users.has_next,
                        'has_prev': users.has_prev
                    }
                }), 200

            except Exception as e:
                return jsonify({'error': 'Failed to fetch users'}), 500

        return _admin_get_users()

    return app

# Create app instance
app = create_app()

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)
