# BLACK SHADOW E-Commerce Platform

A complete, secure e-commerce platform built with Flask backend and vanilla JavaScript frontend. This is a legitimate e-commerce solution for legal goods and services.

## 🚀 Features

### ✅ Completed Features

1. **Enhanced Database Models**
   - User management with profiles and authentication
   - Product catalog with categories, inventory tracking
   - Shopping cart and order management
   - Admin roles and permissions

2. **Security & Authentication System**
   - Secure password hashing with Werkzeug
   - Session-based authentication
   - User registration and login
   - Password strength validation
   - Admin and user role separation

3. **Product Management System**
   - Product categories and descriptions
   - Inventory tracking with stock levels
   - Product search and filtering
   - Image support for products
   - SKU management

4. **Shopping Cart & Checkout**
   - Session-based cart management
   - Add/remove items from cart
   - Secure checkout process
   - Order tracking and history
   - Stock validation during checkout

5. **Admin Panel**
   - Product management (CRUD operations)
   - Order management and status updates
   - User management
   - Category management
   - Admin-only access controls

6. **Frontend Integration**
   - Responsive design with modern UI
   - API integration with backend
   - Real-time cart updates
   - User authentication flow
   - Error handling and notifications

## 🛠️ Technology Stack

### Backend
- **Flask** - Python web framework
- **SQLAlchemy** - Database ORM
- **Flask-CORS** - Cross-origin resource sharing
- **Werkzeug** - Password hashing and security
- **SQLite** - Database (easily replaceable with PostgreSQL/MySQL)

### Frontend
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with flexbox/grid
- **Vanilla JavaScript** - No framework dependencies
- **Fetch API** - HTTP requests to backend

## 📦 Installation & Setup

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd "BLACK SHADOW"
   ```

2. **Run the setup script**
   ```bash
   python start_app.py
   ```

   This script will:
   - Install required dependencies
   - Set up the database
   - Seed sample data
   - Start the development server

### Manual Setup

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up the database**
   ```bash
   cd backend
   python seed_data.py
   cd ..
   ```

3. **Start the server**
   ```bash
   cd backend
   python app.py
   ```

4. **Access the application**
   - Open your browser to `http://localhost:5000`

## 👤 Default Login Credentials

### Admin Account
- **Email:** <EMAIL>
- **Password:** admin123
- **Access:** Full admin panel access

### Regular User Account
- **Email:** <EMAIL>
- **Password:** user123
- **Access:** Shopping and user features

## 🗂️ Project Structure

```
BLACK SHADOW/
├── backend/
│   ├── app.py              # Main Flask application
│   ├── models.py           # Database models
│   ├── routes.py           # API routes
│   ├── auth.py             # Authentication routes
│   ├── config.py           # Configuration settings
│   ├── database.py         # Database initialization
│   └── seed_data.py        # Sample data seeder
├── Frontend/
│   ├── index.html          # Main page
│   ├── login.html          # Login page
│   ├── register.html       # Registration page
│   ├── admin.html          # Admin panel
│   ├── css/                # Stylesheets
│   └── js/                 # JavaScript files
├── requirements.txt        # Python dependencies
├── start_app.py           # Setup and startup script
└── README.md              # This file
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/check-session` - Check login status
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Products
- `GET /api/products` - List products (with pagination/filtering)
- `GET /api/products/<id>` - Get single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/<id>` - Update product (admin only)
- `DELETE /api/products/<id>` - Delete product (admin only)

### Categories
- `GET /api/categories` - List categories
- `POST /api/categories` - Create category (admin only)
- `PUT /api/categories/<id>` - Update category (admin only)

### Shopping Cart
- `GET /api/cart` - Get user's cart
- `POST /api/cart/add` - Add item to cart
- `PUT /api/cart/update/<id>` - Update cart item
- `DELETE /api/cart/remove/<id>` - Remove cart item
- `DELETE /api/cart/clear` - Clear entire cart

### Orders
- `GET /api/orders` - Get user's orders
- `GET /api/orders/<id>` - Get specific order
- `POST /api/checkout` - Create new order
- `GET /api/admin/orders` - Get all orders (admin only)
- `PUT /api/admin/orders/<id>/status` - Update order status (admin only)

## 🔒 Security Features

- Password hashing with Werkzeug
- Session-based authentication
- CSRF protection ready
- Input validation and sanitization
- SQL injection prevention via SQLAlchemy ORM
- XSS protection through proper escaping
- Admin role separation
- Secure cookie configuration

## 🚀 Deployment Considerations

### For Production:
1. Change `SECRET_KEY` in config.py
2. Use PostgreSQL or MySQL instead of SQLite
3. Enable HTTPS
4. Set up proper logging
5. Use a production WSGI server (Gunicorn, uWSGI)
6. Configure reverse proxy (Nginx)
7. Set up monitoring and backups

## 🤝 Contributing

This is a legitimate e-commerce platform for legal goods and services. Contributions are welcome for:
- Bug fixes
- Feature enhancements
- Security improvements
- Documentation updates
- Performance optimizations

## 📄 License

This project is for educational and legitimate business purposes only.

## ⚠️ Important Notes

- This platform is designed for **legitimate e-commerce only**
- All transactions should comply with local laws and regulations
- The platform includes security features but should be audited before production use
- Regular security updates and monitoring are recommended

## 🆘 Support

If you encounter any issues:
1. Check the console for error messages
2. Ensure all dependencies are installed
3. Verify the database is properly set up
4. Check that the server is running on the correct port

For development questions, please refer to the Flask and SQLAlchemy documentation.
