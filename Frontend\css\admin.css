body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: darkred; /* Background color for header */
    color: #333; /* Text color */
}

.logo {
    font-size: 2em;
    font-weight: bold;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
}

nav a {
    color: white;
    text-decoration: none;
}

.admin-product-form {
    padding: 20px;
}

h2 {
    text-align: center;
}

form {
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

form label {
    display: block;
    margin: 10px 0 5px;
}

form input {
    width: calc(100% - 22px);
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background-color: #007bff; /* Blue color */
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #0056b3; /* Darker blue on hover */
}

.existing-products {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; /* Ensures even spacing between products */
    gap: 20px; /* Space between product items */
}

.product-card {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: calc(33.33% - 20px); /* 3 cards per row with space */
    text-align: center;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.product-card img {
    width: 100%;
    height: auto;
}

.product-card h3 {
    margin: 10px 0;
}

.product-card button {
    background-color: #dc3545; /* Red color for delete */
}

.product-card button:hover {
    background-color: #c82333; /* Darker red on hover */
}

footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 10px 0;
    position: relative; /* So it stays at the bottom */
    bottom: 0;
    width: 100%;
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .product-card {
        width: calc(50% - 20px); /* 2 cards per row on smaller screens */
    }
}

@media (max-width: 480px) {
    .product-card {
        width: 100%; /* 1 card per row on very small screens */
    }
}