# Deployment Guide - Black Shadow E-Commerce Platform

This guide covers deploying the Black Shadow e-commerce platform for production use.

## 🚀 Quick Development Setup

### Option 1: Automated Setup
```bash
python start_app.py
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Setup database
cd backend
python seed_data.py

# Start server
python app.py
```

## 🔧 Production Deployment

### Prerequisites
- Python 3.8+
- PostgreSQL or MySQL (recommended for production)
- Nginx (for reverse proxy)
- SSL certificate
- Domain name

### 1. Environment Setup

Create a production configuration:

```python
# backend/config.py - Add production config
class ProductionConfig(Config):
    DEBUG = False
    SECRET_KEY = os.getenv('SECRET_KEY')  # Use strong random key
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL')
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
```

### 2. Database Setup

For PostgreSQL:
```bash
# Install PostgreSQL adapter
pip install psycopg2-binary

# Set environment variable
export DATABASE_URL="postgresql://username:password@localhost/blackshadow"
```

For MySQL:
```bash
# Install MySQL adapter
pip install PyMySQL

# Set environment variable
export DATABASE_URL="mysql+pymysql://username:password@localhost/blackshadow"
```

### 3. Environment Variables

Create a `.env` file:
```bash
SECRET_KEY=your-super-secret-production-key-here
DATABASE_URL=postgresql://user:pass@localhost/blackshadow
FLASK_ENV=production
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 4. WSGI Server Setup

Install Gunicorn:
```bash
pip install gunicorn
```

Create `wsgi.py`:
```python
from backend.app import create_app

app = create_app('production')

if __name__ == "__main__":
    app.run()
```

Start with Gunicorn:
```bash
gunicorn --bind 0.0.0.0:8000 wsgi:app
```

### 5. Nginx Configuration

Create `/etc/nginx/sites-available/blackshadow`:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /path/to/your/app/Frontend/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/blackshadow /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. Systemd Service

Create `/etc/systemd/system/blackshadow.service`:
```ini
[Unit]
Description=Black Shadow E-Commerce Platform
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/app
Environment=PATH=/path/to/your/app/venv/bin
ExecStart=/path/to/your/app/venv/bin/gunicorn --bind 127.0.0.1:8000 wsgi:app
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl daemon-reload
sudo systemctl enable blackshadow
sudo systemctl start blackshadow
```

## 🔒 Security Checklist

### Essential Security Steps:
- [ ] Change default SECRET_KEY
- [ ] Use HTTPS with valid SSL certificate
- [ ] Set up firewall (UFW/iptables)
- [ ] Enable rate limiting
- [ ] Regular security updates
- [ ] Database backups
- [ ] Monitor logs
- [ ] Use strong passwords
- [ ] Enable CSRF protection
- [ ] Validate all inputs

### Additional Security:
- [ ] Set up fail2ban
- [ ] Use a Web Application Firewall (WAF)
- [ ] Implement Content Security Policy
- [ ] Regular security audits
- [ ] Monitor for vulnerabilities

## 📊 Monitoring & Maintenance

### Log Monitoring
```bash
# Application logs
tail -f /var/log/blackshadow/app.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# System logs
journalctl -u blackshadow -f
```

### Database Backups
```bash
# PostgreSQL backup
pg_dump blackshadow > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump blackshadow > $BACKUP_DIR/blackshadow_$DATE.sql
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
```

### Performance Monitoring
- Monitor CPU and memory usage
- Database query performance
- Response times
- Error rates
- User activity

## 🚨 Troubleshooting

### Common Issues:

1. **Database Connection Errors**
   - Check DATABASE_URL
   - Verify database server is running
   - Check firewall settings

2. **Permission Errors**
   - Check file permissions
   - Verify user/group ownership
   - Check SELinux settings

3. **SSL Certificate Issues**
   - Verify certificate validity
   - Check certificate chain
   - Ensure proper Nginx configuration

4. **Performance Issues**
   - Monitor database queries
   - Check server resources
   - Optimize static file serving
   - Consider caching solutions

### Debug Mode
Never run with DEBUG=True in production. For troubleshooting:
```bash
# Temporarily enable detailed logging
export FLASK_ENV=development
# Remember to change back to production
```

## 📈 Scaling Considerations

### Horizontal Scaling:
- Load balancer (HAProxy/Nginx)
- Multiple application servers
- Database clustering
- Redis for session storage
- CDN for static files

### Performance Optimization:
- Database indexing
- Query optimization
- Caching (Redis/Memcached)
- Static file optimization
- Image compression

## 🔄 Updates & Maintenance

### Regular Tasks:
1. Security updates
2. Database maintenance
3. Log rotation
4. Backup verification
5. Performance monitoring
6. SSL certificate renewal

### Update Process:
1. Backup database
2. Test in staging environment
3. Deploy during low-traffic hours
4. Monitor for issues
5. Rollback if necessary

## 📞 Support

For deployment issues:
1. Check logs for error messages
2. Verify all configuration files
3. Test database connectivity
4. Ensure all services are running
5. Check firewall and network settings

Remember: This platform is designed for legitimate e-commerce only. Ensure compliance with all applicable laws and regulations in your jurisdiction.
