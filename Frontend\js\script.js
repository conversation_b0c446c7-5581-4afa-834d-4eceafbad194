// script.js
document.addEventListener('DOMContentLoaded', () => {
    const buttons = document.querySelectorAll('button');

    buttons.forEach(button => {
        button.addEventListener('click', () => {
            alert('Added to Cart!');
        });
    });
});
function searchProducts() {
    const query = document.getElementById('searchInput').value;
    // Implement search logic here, for now, just alert the query
    alert('Searching for: ' + query);
}