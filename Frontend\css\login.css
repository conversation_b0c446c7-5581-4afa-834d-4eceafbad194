/* styles.css */

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

header {
    text-align: center;
    background: darkred;
    margin-bottom: 20px;
    position: center; /* Position it absolutely */
    top: 40px; /* Adjust the top space as needed */
    left: 0;
    right: 0;
}

.logo {
    font-size: 4rem;
    font-weight: bold;
    color: #333;
}

.login {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 300px; /* Set a fixed width for better layout */
    margin: auto; /* Center it horizontally */
    margin-top: 100px; /* Space below header */
}

.login h2 {
    margin-bottom: 20px;
    text-align: center;
}

form {
    display: flex;
    flex-direction: column; /* Change to vertical layout */
    align-items: stretch; /* Ensure items take full width */
}

form label {
    margin-bottom: 5px; /* Spacing between label and input */
}

form input,
form select {
    padding: 8px;
    margin-bottom: 15px; /* Spacing between inputs */
    border: 1px solid #ccc;
    border-radius: 4px;
}

form button {
    padding: 10px;
    background-color: #007BFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

form button:hover {
    background-color: #0056b3;
}

#message {
    margin-top: 15px;
    color: red;
    text-align: center;
}