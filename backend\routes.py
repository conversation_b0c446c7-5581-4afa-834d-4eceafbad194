from flask import jsonify, request, session, current_app
from sqlalchemy import or_, and_
from werkzeug.utils import secure_filename
import os
import uuid
from datetime import datetime
from models import db, Product, Order, OrderItem, Category, CartItem, User
from auth import login_required, admin_required

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def register_routes(app):

    # ============ PRODUCT ROUTES ============

    @app.route("/api/products", methods=["GET"])
    def get_products():
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', app.config['PRODUCTS_PER_PAGE'], type=int)
            category_id = request.args.get('category_id', type=int)
            search = request.args.get('search', '')
            sort_by = request.args.get('sort_by', 'name')  # name, price, created_at
            sort_order = request.args.get('sort_order', 'asc')  # asc, desc
            min_price = request.args.get('min_price', type=float)
            max_price = request.args.get('max_price', type=float)
            in_stock_only = request.args.get('in_stock_only', 'false').lower() == 'true'

            # Build query
            query = Product.query.filter(Product.is_active == True)

            # Apply filters
            if category_id:
                query = query.filter(Product.category_id == category_id)

            if search:
                search_term = f"%{search}%"
                query = query.filter(
                    or_(
                        Product.name.ilike(search_term),
                        Product.description.ilike(search_term),
                        Product.sku.ilike(search_term)
                    )
                )

            if min_price is not None:
                query = query.filter(Product.price >= min_price)

            if max_price is not None:
                query = query.filter(Product.price <= max_price)

            if in_stock_only:
                query = query.filter(Product.stock_quantity > 0)

            # Apply sorting
            if sort_by == 'price':
                if sort_order == 'desc':
                    query = query.order_by(Product.price.desc())
                else:
                    query = query.order_by(Product.price.asc())
            elif sort_by == 'created_at':
                if sort_order == 'desc':
                    query = query.order_by(Product.created_at.desc())
                else:
                    query = query.order_by(Product.created_at.asc())
            else:  # name
                if sort_order == 'desc':
                    query = query.order_by(Product.name.desc())
                else:
                    query = query.order_by(Product.name.asc())

            # Paginate
            products = query.paginate(
                page=page, per_page=per_page, error_out=False
            )

            return jsonify({
                'products': [product.to_dict() for product in products.items],
                'pagination': {
                    'page': products.page,
                    'pages': products.pages,
                    'per_page': products.per_page,
                    'total': products.total,
                    'has_next': products.has_next,
                    'has_prev': products.has_prev
                }
            }), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch products'}), 500

    @app.route("/api/products/<int:product_id>", methods=["GET"])
    def get_product(product_id):
        try:
            product = Product.query.filter_by(id=product_id, is_active=True).first()
            if not product:
                return jsonify({'error': 'Product not found'}), 404

            return jsonify({'product': product.to_dict()}), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch product'}), 500

    @app.route("/api/products", methods=["POST"])
    @admin_required
    def create_product():
        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ['name', 'price', 'category_id']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            # Check if category exists
            category = Category.query.get(data['category_id'])
            if not category:
                return jsonify({'error': 'Category not found'}), 404

            # Generate SKU if not provided
            sku = data.get('sku')
            if not sku:
                sku = f"PROD-{uuid.uuid4().hex[:8].upper()}"

            # Check if SKU is unique
            if Product.query.filter_by(sku=sku).first():
                return jsonify({'error': 'SKU already exists'}), 400

            product = Product(
                name=data['name'],
                description=data.get('description', ''),
                price=data['price'],
                cost_price=data.get('cost_price'),
                sku=sku,
                stock_quantity=data.get('stock_quantity', 0),
                min_stock_level=data.get('min_stock_level', 5),
                weight=data.get('weight'),
                dimensions=data.get('dimensions'),
                image_url=data.get('image_url'),
                additional_images=data.get('additional_images'),
                is_featured=data.get('is_featured', False),
                category_id=data['category_id']
            )

            db.session.add(product)
            db.session.commit()

            return jsonify({
                'message': 'Product created successfully',
                'product': product.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to create product'}), 500

    @app.route("/api/products/<int:product_id>", methods=["PUT"])
    @admin_required
    def update_product(product_id):
        try:
            product = Product.query.get(product_id)
            if not product:
                return jsonify({'error': 'Product not found'}), 404

            data = request.get_json()

            # Update fields if provided
            if 'name' in data:
                product.name = data['name']
            if 'description' in data:
                product.description = data['description']
            if 'price' in data:
                product.price = data['price']
            if 'cost_price' in data:
                product.cost_price = data['cost_price']
            if 'stock_quantity' in data:
                product.stock_quantity = data['stock_quantity']
            if 'min_stock_level' in data:
                product.min_stock_level = data['min_stock_level']
            if 'weight' in data:
                product.weight = data['weight']
            if 'dimensions' in data:
                product.dimensions = data['dimensions']
            if 'image_url' in data:
                product.image_url = data['image_url']
            if 'additional_images' in data:
                product.additional_images = data['additional_images']
            if 'is_featured' in data:
                product.is_featured = data['is_featured']
            if 'is_active' in data:
                product.is_active = data['is_active']
            if 'category_id' in data:
                category = Category.query.get(data['category_id'])
                if not category:
                    return jsonify({'error': 'Category not found'}), 404
                product.category_id = data['category_id']

            product.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'message': 'Product updated successfully',
                'product': product.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update product'}), 500

    @app.route("/api/products/<int:product_id>", methods=["DELETE"])
    @admin_required
    def delete_product(product_id):
        try:
            product = Product.query.get(product_id)
            if not product:
                return jsonify({'error': 'Product not found'}), 404

            # Soft delete - just mark as inactive
            product.is_active = False
            db.session.commit()

            return jsonify({'message': 'Product deleted successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to delete product'}), 500

    # ============ CATEGORY ROUTES ============

    @app.route("/api/categories", methods=["GET"])
    def get_categories():
        try:
            categories = Category.query.filter_by(is_active=True).all()
            return jsonify({
                'categories': [category.to_dict() for category in categories]
            }), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch categories'}), 500

    @app.route("/api/categories", methods=["POST"])
    @admin_required
    def create_category():
        try:
            data = request.get_json()

            if not data.get('name'):
                return jsonify({'error': 'Category name is required'}), 400

            # Check if category name already exists
            if Category.query.filter_by(name=data['name']).first():
                return jsonify({'error': 'Category name already exists'}), 400

            category = Category(
                name=data['name'],
                description=data.get('description', ''),
                image_url=data.get('image_url')
            )

            db.session.add(category)
            db.session.commit()

            return jsonify({
                'message': 'Category created successfully',
                'category': category.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to create category'}), 500

    @app.route("/api/categories/<int:category_id>", methods=["PUT"])
    @admin_required
    def update_category(category_id):
        try:
            category = Category.query.get(category_id)
            if not category:
                return jsonify({'error': 'Category not found'}), 404

            data = request.get_json()

            if 'name' in data:
                # Check if new name already exists (excluding current category)
                existing = Category.query.filter(
                    Category.name == data['name'],
                    Category.id != category_id
                ).first()
                if existing:
                    return jsonify({'error': 'Category name already exists'}), 400
                category.name = data['name']

            if 'description' in data:
                category.description = data['description']
            if 'image_url' in data:
                category.image_url = data['image_url']
            if 'is_active' in data:
                category.is_active = data['is_active']

            db.session.commit()

            return jsonify({
                'message': 'Category updated successfully',
                'category': category.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update category'}), 500

    # ============ CART ROUTES ============

    @app.route("/api/cart", methods=["GET"])
    @login_required
    def get_cart():
        try:
            user_id = session['user_id']
            cart_items = CartItem.query.filter_by(user_id=user_id).all()

            total = sum(item.product.price * item.quantity for item in cart_items if item.product)

            return jsonify({
                'cart_items': [item.to_dict() for item in cart_items],
                'total': float(total),
                'item_count': len(cart_items)
            }), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch cart'}), 500

    @app.route("/api/cart/add", methods=["POST"])
    @login_required
    def add_to_cart():
        try:
            user_id = session['user_id']
            data = request.get_json()

            if not data.get('product_id') or not data.get('quantity'):
                return jsonify({'error': 'Product ID and quantity are required'}), 400

            product_id = data['product_id']
            quantity = data['quantity']

            if quantity <= 0:
                return jsonify({'error': 'Quantity must be positive'}), 400

            # Check if product exists and is active
            product = Product.query.filter_by(id=product_id, is_active=True).first()
            if not product:
                return jsonify({'error': 'Product not found'}), 404

            # Check stock availability
            if product.stock_quantity < quantity:
                return jsonify({'error': 'Insufficient stock'}), 400

            # Check if item already in cart
            existing_item = CartItem.query.filter_by(
                user_id=user_id,
                product_id=product_id
            ).first()

            if existing_item:
                # Update quantity
                new_quantity = existing_item.quantity + quantity
                if product.stock_quantity < new_quantity:
                    return jsonify({'error': 'Insufficient stock'}), 400

                existing_item.quantity = new_quantity
                existing_item.updated_at = datetime.utcnow()
            else:
                # Create new cart item
                cart_item = CartItem(
                    user_id=user_id,
                    product_id=product_id,
                    quantity=quantity
                )
                db.session.add(cart_item)

            db.session.commit()

            return jsonify({'message': 'Item added to cart successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to add item to cart'}), 500

    @app.route("/api/cart/update/<int:item_id>", methods=["PUT"])
    @login_required
    def update_cart_item(item_id):
        try:
            user_id = session['user_id']
            data = request.get_json()

            if not data.get('quantity'):
                return jsonify({'error': 'Quantity is required'}), 400

            quantity = data['quantity']

            if quantity <= 0:
                return jsonify({'error': 'Quantity must be positive'}), 400

            cart_item = CartItem.query.filter_by(id=item_id, user_id=user_id).first()
            if not cart_item:
                return jsonify({'error': 'Cart item not found'}), 404

            # Check stock availability
            if cart_item.product.stock_quantity < quantity:
                return jsonify({'error': 'Insufficient stock'}), 400

            cart_item.quantity = quantity
            cart_item.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({'message': 'Cart item updated successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update cart item'}), 500

    @app.route("/api/cart/remove/<int:item_id>", methods=["DELETE"])
    @login_required
    def remove_cart_item(item_id):
        try:
            user_id = session['user_id']
            cart_item = CartItem.query.filter_by(id=item_id, user_id=user_id).first()

            if not cart_item:
                return jsonify({'error': 'Cart item not found'}), 404

            db.session.delete(cart_item)
            db.session.commit()

            return jsonify({'message': 'Item removed from cart successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to remove cart item'}), 500

    @app.route("/api/cart/clear", methods=["DELETE"])
    @login_required
    def clear_cart():
        try:
            user_id = session['user_id']
            CartItem.query.filter_by(user_id=user_id).delete()
            db.session.commit()

            return jsonify({'message': 'Cart cleared successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to clear cart'}), 500

    # ============ ORDER ROUTES ============

    @app.route("/api/orders", methods=["GET"])
    @login_required
    def get_user_orders():
        try:
            user_id = session['user_id']
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', app.config['ORDERS_PER_PAGE'], type=int)

            orders = Order.query.filter_by(user_id=user_id).order_by(
                Order.created_at.desc()
            ).paginate(page=page, per_page=per_page, error_out=False)

            return jsonify({
                'orders': [order.to_dict() for order in orders.items],
                'pagination': {
                    'page': orders.page,
                    'pages': orders.pages,
                    'per_page': orders.per_page,
                    'total': orders.total,
                    'has_next': orders.has_next,
                    'has_prev': orders.has_prev
                }
            }), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch orders'}), 500

    @app.route("/api/orders/<int:order_id>", methods=["GET"])
    @login_required
    def get_order(order_id):
        try:
            user_id = session['user_id']
            order = Order.query.filter_by(id=order_id, user_id=user_id).first()

            if not order:
                return jsonify({'error': 'Order not found'}), 404

            return jsonify({'order': order.to_dict()}), 200

        except Exception as e:
            return jsonify({'error': 'Failed to fetch order'}), 500

    @app.route("/api/checkout", methods=["POST"])
    @login_required
    def checkout():
        try:
            user_id = session['user_id']
            data = request.get_json()

            # Validate required shipping information
            required_fields = [
                'shipping_name', 'shipping_address', 'shipping_city',
                'shipping_postal_code', 'shipping_country'
            ]
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            # Get cart items
            cart_items = CartItem.query.filter_by(user_id=user_id).all()
            if not cart_items:
                return jsonify({'error': 'Cart is empty'}), 400

            # Validate stock availability and calculate total
            total_amount = 0
            order_items_data = []

            for cart_item in cart_items:
                product = cart_item.product
                if not product or not product.is_active:
                    return jsonify({'error': f'Product {product.name if product else "unknown"} is no longer available'}), 400

                if product.stock_quantity < cart_item.quantity:
                    return jsonify({'error': f'Insufficient stock for {product.name}'}), 400

                item_total = product.price * cart_item.quantity
                total_amount += item_total

                order_items_data.append({
                    'product': product,
                    'quantity': cart_item.quantity,
                    'unit_price': product.price,
                    'total_price': item_total
                })

            # Generate unique order number
            order_number = f"ORD-{datetime.utcnow().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"

            # Create order
            order = Order(
                order_number=order_number,
                user_id=user_id,
                total_amount=total_amount,
                shipping_cost=data.get('shipping_cost', 0),
                tax_amount=data.get('tax_amount', 0),
                discount_amount=data.get('discount_amount', 0),
                shipping_name=data['shipping_name'],
                shipping_address=data['shipping_address'],
                shipping_city=data['shipping_city'],
                shipping_postal_code=data['shipping_postal_code'],
                shipping_country=data['shipping_country'],
                shipping_phone=data.get('shipping_phone'),
                payment_method=data.get('payment_method', 'pending')
            )

            db.session.add(order)
            db.session.flush()  # Get order ID

            # Create order items and update stock
            for item_data in order_items_data:
                order_item = OrderItem(
                    order_id=order.id,
                    product_id=item_data['product'].id,
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['total_price'],
                    product_name=item_data['product'].name,
                    product_sku=item_data['product'].sku
                )
                db.session.add(order_item)

                # Update product stock
                item_data['product'].stock_quantity -= item_data['quantity']

            # Clear cart
            CartItem.query.filter_by(user_id=user_id).delete()

            db.session.commit()

            return jsonify({
                'message': 'Order placed successfully',
                'order': order.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to place order'}), 500
