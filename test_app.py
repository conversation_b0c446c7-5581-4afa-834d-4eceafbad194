#!/usr/bin/env python3
"""
Simple test script to verify the Black Shadow E-commerce Platform is working correctly.
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:5000'
TEST_USER = {
    'username': 'testuser',
    'email': '<EMAIL>',
    'password': 'testpass123',
    'full_name': 'Test User'
}

def test_server_running():
    """Test if the server is running"""
    try:
        response = requests.get(f'{BASE_URL}/')
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False

def test_user_registration():
    """Test user registration"""
    try:
        response = requests.post(
            f'{BASE_URL}/api/auth/register',
            json=TEST_USER,
            timeout=10
        )
        return response.status_code in [201, 400]  # 400 if user already exists
    except:
        return False

def test_user_login():
    """Test user login"""
    try:
        response = requests.post(
            f'{BASE_URL}/api/auth/login',
            json={
                'username': TEST_USER['email'],
                'password': TEST_USER['password']
            },
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

def test_products_api():
    """Test products API"""
    try:
        response = requests.get(f'{BASE_URL}/api/products', timeout=10)
        return response.status_code == 200
    except:
        return False

def test_categories_api():
    """Test categories API"""
    try:
        response = requests.get(f'{BASE_URL}/api/categories', timeout=10)
        return response.status_code == 200
    except:
        return False

def run_tests():
    """Run all tests"""
    print("=" * 60)
    print("BLACK SHADOW E-COMMERCE PLATFORM - TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Server Running", test_server_running),
        ("User Registration", test_user_registration),
        ("User Login", test_user_login),
        ("Products API", test_products_api),
        ("Categories API", test_categories_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"Testing {test_name}...", end=" ")
        try:
            result = test_func()
            if result:
                print("✓ PASS")
                results.append(True)
            else:
                print("✗ FAIL")
                results.append(False)
        except Exception as e:
            print(f"✗ ERROR: {e}")
            results.append(False)
        
        time.sleep(0.5)  # Small delay between tests
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! The platform is working correctly.")
    elif passed > total // 2:
        print("⚠️  Most tests passed. Some features may need attention.")
    else:
        print("❌ Many tests failed. Please check the server and configuration.")
    
    print("\n" + "=" * 60)
    print("NEXT STEPS")
    print("=" * 60)
    
    if not results[0]:  # Server not running
        print("1. Start the server by running: python start_app.py")
        print("2. Or manually: cd backend && python app.py")
    else:
        print("1. Open your browser to: http://localhost:5000")
        print("2. Try logging in with:")
        print("   - Admin: <EMAIL> / admin123")
        print("   - User:  <EMAIL> / user123")
        print("3. Test the shopping cart and checkout process")
        print("4. Access admin panel at: http://localhost:5000/admin.html")
    
    return passed == total

def main():
    """Main function"""
    success = run_tests()
    
    if success:
        print("\n🚀 Platform is ready for use!")
    else:
        print("\n🔧 Please address the failed tests before using the platform.")

if __name__ == "__main__":
    main()
